import chroma from 'chroma-js'
import { useMediaQuery } from '@mui/material'
import { useAppSelector, useAppDispatch } from '../../hooks/store'
import { setLoading } from '../../store/wheel'
import dingSound from '../../assets/ding.mp3'
import applauseSound from '../../assets/applause.mp3'
import ConfettiExplosion from '../Confetti'
import { useRef, useMemo, type ElementRef, useState } from 'react'
import useAudioContext from '../../hooks/useAudioContext'

import styled from 'styled-components'

const Header = styled.h1`
  color: white;
  font-size: 2em;
  text-align: center;
  animation: fade-in-out 2s ease infinite;

  @keyframes fade-in-out {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
`
const getContrastRatio = (color1, color2) => chroma.contrast(color1, color2)

const determineTextColor = hexCode => {
  const color = chroma(hexCode)
  const contrastWithWhite = getContrastRatio(color, 'white')
  const contrastWithBlack = getContrastRatio(color, 'black')

  return contrastWithWhite > contrastWithBlack ? 'white' : 'black'
}

// var FirstTimeRender = null

import React from 'react'

var previousIndex = -1
var varWheelClicked = false
var firstTime = true
var canvasContext = null
var winningSegment
var winningSegmentIndex
var winningSegmentStartAngle
var winningSegmentEndAngle
var segColors
var segments
var spinning = false

var WheelComponent = function WheelComponent(_ref) {
  var onFinished = _ref.onFinished,
    _ref$primaryColor = _ref.primaryColor,
    primaryColor = _ref$primaryColor === void 0 ? 'black' : _ref$primaryColor,
    _ref$contrastColor = _ref.contrastColor,
    contrastColor = _ref$contrastColor === void 0 ? 'white' : _ref$contrastColor,
    _ref$buttonText = _ref.buttonText,
    buttonText = _ref$buttonText === void 0 ? 'Spin' : _ref$buttonText,
    _ref$isOnlyOnce = _ref.isOnlyOnce,
    isOnlyOnce = _ref$isOnlyOnce === void 0 ? true : _ref$isOnlyOnce,
    _ref$upDuration = _ref.upDuration,
    upDuration = _ref$upDuration === void 0 ? 200 : _ref$upDuration,
    _ref$downDuration = _ref.downDuration,
    downDuration = _ref$downDuration === void 0 ? 20000 : _ref$downDuration,
    _ref$fontFamily = _ref.fontFamily,
    fontFamily = _ref$fontFamily === void 0 ? 'proxima-nova' : _ref$fontFamily
  var currentSegment: any = ''
  var isStarted = false

  const dispatch = useAppDispatch()

  const { names, colors } = useAppSelector(state => state.wheel)

  const confettiButton = useRef<ElementRef<'button'>>(null)
  // @ts-ignore
  const playDingAudio = useAudioContext(dingSound)
  const applauseAudioRef = useRef<HTMLAudioElement | null>(null)
  const [confettiKey, setConfettiKey] = useState<number>(Date.now())

  // Add state for current segment to trigger arrow color updates during rotation
  const [currentArrowSegment, setCurrentArrowSegment] = useState<number>(0)
  const [currentArrowColor, setCurrentArrowColor] = useState<string>('#3369E8')
  const [forceUpdate, setForceUpdate] = useState<number>(0)
   // Replace the getSegments function with this updated version:

// Replace the getSegments function with this updated version:

// Replace your existing getSegments function with this updated version:

// Replace your existing getSegments function with this updated version:

const getSegments = () => {
  const namesArray = names.split('\n')
  const filteredNames = namesArray.filter(name => name !== '')

  // Separate names with and without commas
  const namesWithCommas = filteredNames.filter(name => name.includes(','))
  const namesWithoutCommas = filteredNames.filter(name => !name.includes(','))

  // Shuffle only the names without commas
  const shuffledNamesWithoutCommas = [...namesWithoutCommas]
  for (let i = shuffledNamesWithoutCommas.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffledNamesWithoutCommas[i], shuffledNamesWithoutCommas[j]] = 
    [shuffledNamesWithoutCommas[j], shuffledNamesWithoutCommas[i]];
  }

  // Combine: shuffled names without commas + original names with commas
  const orderedNames = [...shuffledNamesWithoutCommas, ...namesWithCommas]

  // Remove duplicates while preserving order
  const uniqueNames = []
  const seenNames = new Set()
  
  orderedNames.forEach(name => {
    const cleanName = name.includes(',') ? name.replace(/,/g, '') : name
    if (!seenNames.has(cleanName)) {
      seenNames.add(cleanName)
      uniqueNames.push(cleanName)
    }
  })

  // Generate colors using all available colors with no adjacent duplicates
  const generateNonAdjacentColors = (colorPalette, segmentCount) => {
    if (segmentCount === 0) return []
    if (segmentCount === 1) return [colorPalette[0]]
    if (colorPalette.length === 1) {
      // If only one color available, use it for all segments
      return new Array(segmentCount).fill(colorPalette[0])
    }
    
    const result = []
    const totalColors = colorPalette.length
    
    // Create an optimal color sequence that uses all colors and avoids adjacency
    for (let i = 0; i < segmentCount; i++) {
      let colorIndex
      
      if (i === 0) {
        // First segment gets the first color
        colorIndex = 0
      } else {
        // Calculate next color index ensuring it's different from previous
        const prevColorIndex = colorPalette.indexOf(result[i - 1])
        
        // Try the next color in sequence first
        colorIndex = (prevColorIndex + 1) % totalColors
        
        // If we're at the last segment and it would match the first segment (circular wheel)
        if (i === segmentCount - 1 && segmentCount > 2 && totalColors > 2) {
          const firstColorIndex = colorPalette.indexOf(result[0])
          if (colorIndex === firstColorIndex) {
            // Find an alternative that's different from both previous and first
            colorIndex = (colorIndex + 1) % totalColors
            // If still matches first, try one more
            if (colorIndex === firstColorIndex) {
              colorIndex = (colorIndex + 1) % totalColors
            }
          }
        }
      }
      
      result.push(colorPalette[colorIndex])
    }
    
    return result
  }

  // Generate colors with no adjacent duplicates, using all available colors
  const segmentColors = generateNonAdjacentColors(colors, uniqueNames.length)

  // Return both names and colors as separate arrays
  return {
    names: uniqueNames,
    colors: segmentColors
  }
}
// Update the useMemo hooks to use the new structure:
const memoizedSegmentsData = useMemo(() => getSegments(), [names, colors])
const memoizedSegments = useMemo(() => memoizedSegmentsData.names, [memoizedSegmentsData])
const memoizedSegColors = useMemo(() => memoizedSegmentsData.colors, [memoizedSegmentsData])

  winningSegment = _ref.winningSegment

  function generateColorArray(arr: string[], length: number) {
    if (arr.length === 0 || length <= 0) {
      return []
    }

    const result = []
    let currentIndex = 0

    for (let i = 0; i < length; i++) {
      result.push(arr[currentIndex])
      currentIndex =
        i % 2 === 0 ? (currentIndex + 1) % arr.length : (arr.length - currentIndex - 1) % arr.length
    }

    return result
  }

 
  // Update global variables for compatibility with existing code
  segments = memoizedSegments
  segColors = memoizedSegColors

  var _useState = React.useState(false),
    isFinished = _useState[0],
    _setFinished = _useState[1] // Prefix with underscore to indicate it's not used

  var lengthConstant = 12 // Controls overall speed
  var slowDownProgress = 0.4 // Controls deceleration
  var winningProgress = 1.5 // Controls final approach
  var timerHandle = 0

  var angleCurrent = 0
  var angleDelta = 0

  var maxSpeed = 35 // Initial maximum speed
  var upTime = lengthConstant * upDuration * 2 // Acceleration time
  var downTime = lengthConstant * downDuration * 2.35 // Deceleration time

  // Higher frame rate for ultra-smooth animation
  var frameRate = 120 // Target 120fps for extremely smooth animation

  var spinStart = 0
  var frames = 0

  // const viewport = useViewport()
  const [wheelClicked, setWheelClicked] = React.useState(varWheelClicked)
  const [firstLoad, setFirstLoad] = React.useState(true)
  const [hasBeenSpun, setHasBeenSpun] = React.useState(false)
  const isMobileWidth = useMediaQuery('(max-height: 700px)')
  const isMobileHeight = useMediaQuery('(max-width: 700px)')
  const isLargeScreenWidth = useMediaQuery('(min-width: 1001px)')
  const isLargeScreenHeight = useMediaQuery('(min-height: 1150px)')

  const isLargeScreen = isLargeScreenWidth && isLargeScreenHeight
  const isMobile = false
  // const isMobile = isMobileWidth || isMobileHeigh


  var centerX = isMobile ? 170 : isLargeScreen ? 340 : 285
  var centerY = isMobile ? 170 : isLargeScreen ? 340 : 285

  const size = isMobile ? 160 : isLargeScreen ? 320 : 270

  const handleKeyDown = event => {
    if (event.ctrlKey && event.key === 'Enter') {
      setWheelClicked(true)
      varWheelClicked = true
      spin()
    }
  }

  React.useEffect(function () {
    setTimeout(function () {
      window.scrollTo(0, 1)
    }, 0)
    const onKeyDown = event => handleKeyDown(event)

    // Adding event listener to the window
    window.addEventListener('keydown', onKeyDown)

    return () => {
      window.removeEventListener('keydown', onKeyDown)
    }
  }, [])

  React.useEffect(
    function () {
      // Reset wheel state when names or segments change
      resetWheel()

      if (!firstLoad && names.trim() !== '') {
        wheelInit() // Always reinitialize to ensure segments are updated
      } else if (!firstLoad && names.trim() === '') {
        setFirstLoad(true)
      } else {
        wheelInit()
        setFirstLoad(false)
      }
    },
    [names, colors, memoizedSegments.length] // Include segments length to trigger re-render
  )

  // Initialize arrow color when segments or colors change
  React.useEffect(() => {
    if (segColors && segColors.length > 0) {
      setCurrentArrowSegment(0)
      setCurrentArrowColor(segColors[0])
    }
  }, [segColors])

  var wheelInit = function wheelInit() {
    initCanvas()
    wheelDraw()

    // Initialize arrow color to the first segment's color
    if (segColors && segColors.length > 0) {
      setCurrentArrowSegment(0)
      setCurrentArrowColor(segColors[0])
    }
  }

  // Add canvasRef to track canvas element
  const canvasRef = useRef<HTMLCanvasElement | null>(null)

  // Update initCanvas function
  var initCanvas = function initCanvas() {
    const canvas = canvasRef.current
    if (canvas) {
      canvas.removeEventListener('click', spin)
      canvas.addEventListener('click', spin, false)
      canvasContext = canvas.getContext('2d')

      // Ensure canvas is cleared and dimensions are set
      canvas.width = centerX * 2
      canvas.height = centerY * 2

      if (canvasContext) {
        canvasContext.clearRect(0, 0, canvas.width, canvas.height)
      }
    }
  }

  // Update the resetWheel function for consistency with other improvements
  const resetWheel = () => {
    if (timerHandle) {
      clearInterval(timerHandle)
    }

    // Reset all wheel state variables
    spinning = false
    angleCurrent = 0
    angleDelta = 0
    frames = 0
    timerHandle = 0
    previousSegmentIndex = null
    numberOfSpins = 0
    varWheelClicked = false
    setWheelClicked(false)
    setHasBeenSpun(false)

    // Reset arrow color to first segment
    if (segColors && segColors.length > 0) {
      setCurrentArrowSegment(0)
      setCurrentArrowColor(segColors[0])
    }

    // Clear canvas completely
    const canvas = canvasRef.current
    if (canvas && canvasContext) {
      canvasContext.clearRect(0, 0, canvas.width, canvas.height)
    }
  }


  var spin = function spin() {
    // Prevent multiple spins
    if (spinning) return

    // Validate that we have segments to spin
    if (!segments || segments.length === 0) {
      console.warn("Cannot spin: No segments available")
      return
    }

    // Clear any existing state
    if (timerHandle) {
      clearInterval(timerHandle)
      timerHandle = 0
    }

    // Reset all states
    spinning = false
    angleCurrent = 0
    angleDelta = 0
    frames = 0
    previousSegmentIndex = null
    numberOfSpins = 0  // Reset spin counter

    // Calculate the starting segment index
    const segmentAngle = (2 * Math.PI) / segments.length;
    startingSegmentIndex = Math.floor(angleCurrent / segmentAngle) % segments.length;
    initialAngle = angleCurrent;


    // Reinitialize the wheel
    initCanvas()
    wheelDraw()

    // Start new spin
    setWheelClicked(true)
    varWheelClicked = true
    setHasBeenSpun(true)
    spinning = true
    dispatch(setLoading(true))




    spinStart = new Date().getTime()
    // Calculate maxSpeed based on the new parameters for smoother animation
    maxSpeed = Math.PI / (lengthConstant * 0.85)
    frames = 0
    // Use higher frame rate for smoother animation (1000ms / 60fps ≈ 16.67ms)
    timerHandle = setInterval(onTimerTick, Math.floor(1000 / frameRate))
  }

  let previousSegmentIndex = null
  let numberOfSpins = 0
  let startingSegmentIndex = 0
  let initialAngle = 0

  var onTimerTick = function onTimerTick() {
    frames++
    draw()
    var duration = new Date().getTime() - spinStart

    // Safety check - ensure we have segments
    if (!segments || segments.length === 0) {
      console.error("No segments available for wheel")
      clearInterval(timerHandle)
      spinning = false
      return
    }

    const segmentAngle = (2 * Math.PI) / segments.length
    var progress = 0
    var finished = false

    // Set target angle to exactly 0 degrees (starting position)
    // This ensures the wheel stops at its starting position after completing rotations
    const targetAngle = 0; // 0 degrees (starting position)

    // Acceleration phase - smoother start with cubic easing
    if (duration < upTime) {
      progress = duration / upTime;
      // Improved acceleration curve using cubic easing for smoother start
      angleDelta = maxSpeed * Math.pow(progress, 3);
    }
    // Deceleration phase
    else {
      progress = Math.min(1, (duration - upTime) / downTime);

      // Target exactly 10.1 rotations as requested
      let targetRotations = 10.08;

   
            const currentAngle = angleCurrent % (2.1 * Math.PI);

      // Calculate how many full rotations we've completed
      const totalRotations = numberOfSpins + (currentAngle / (2.15 * Math.PI));


      // Check if we need to complete more rotations to reach exactly 10
      if (totalRotations < targetRotations) {
        // Still need to complete rotations - maintain speed with ultra-smooth transitions
        const remainingRotations = targetRotations - totalRotations;



        // Create a more gradual, multi-phase deceleration for smoother experience
        if (remainingRotations < 1) {
          // Final rotation - very smooth deceleration curve
          // Use a higher-order polynomial for extremely smooth deceleration
          const slowdownFactor = Math.pow(Math.max(0.05, remainingRotations), 2.5);
          angleDelta = maxSpeed * slowdownFactor;
        } else if (remainingRotations < 2) {
          // Second-to-last rotation - begin gentle slowdown
          // Blend between phases with cubic easing
          const blendFactor = (remainingRotations - 1) / 1; // 0-1 range
          const slowdownFactor = 0.6 + (0.4 * Math.pow(blendFactor, 3));
          angleDelta = maxSpeed * slowdownFactor;
        } else if (remainingRotations < 3) {
          // Third-to-last rotation - very subtle speed reduction
          // Creates anticipation of slowdown without being noticeable
          const blendFactor = (remainingRotations - 2) / 1; // 0-1 range
          const slowdownFactor = 0.85 + (0.15 * Math.pow(blendFactor, 2));
          angleDelta = maxSpeed * slowdownFactor;
        } else {
          // Earlier rotations - maintain consistent speed
          // Apply very slight variations for more natural feel
          const naturalVariation = 0.98 + (0.04 * Math.sin(totalRotations * Math.PI));
          angleDelta = maxSpeed * naturalVariation;
        }
      } else {
        // Check if we've completed exactly 10.1 rotations
        if (Math.abs(totalRotations - 10.1) < 0.01) {

        } else if (totalRotations < 10.09) {
          // If we're slightly under 10.1 rotations, continue rotating

          // Boost speed slightly to reach 10.1 rotations
          angleDelta = Math.max(angleDelta, 0.002);
        }

        // We've completed rotations, now target 0 degrees (starting position) with ultra-smooth approach
        let angleToTarget = (targetAngle - currentAngle + 2 * Math.PI) % (2 * Math.PI);

        // Ensure we always approach the target from the correct direction
        if (angleToTarget > Math.PI) {
          angleToTarget = angleToTarget - 2 * Math.PI;
        }

        // Ultra-enhanced smooth deceleration curve using a sophisticated blend of functions
        // This creates an exceptionally natural feel with absolutely no sudden changes in speed
        const progressFactor = Math.pow(1 - progress, 4) * (1 + Math.sin(progress * Math.PI / 2) * slowDownProgress);
        const baseSpeed = maxSpeed * progressFactor;

        // Highly refined targeting with extremely smooth approach
        // Using a higher-order polynomial for more gradual speed changes
        const targetingFactor = Math.min(1, Math.pow(Math.abs(angleToTarget) / (Math.PI / 4), 0.7));
        let targetSpeed = baseSpeed * (0.85 + 0.15 * targetingFactor);

        // Multi-phase approach system for ultimate smoothness
        if (Math.abs(angleToTarget) < segmentAngle * winningProgress) {
          // Phase 1: Initial approach - smooth cubic function
          const approachFactor = Math.pow(Math.abs(angleToTarget) / (segmentAngle * winningProgress), 1.8);
          targetSpeed = Math.max(0.004, Math.min(targetSpeed, Math.abs(angleToTarget) * approachFactor * 1.2));

          // Phase 2: Mid-close approach - extra smoothing
          if (Math.abs(angleToTarget) < segmentAngle * 0.4) {
            // Apply additional smoothing with higher-order polynomial
            const midApproachFactor = Math.pow(Math.abs(angleToTarget) / (segmentAngle * 0.4), 0.7);
            targetSpeed = Math.max(0.003, targetSpeed * midApproachFactor);

            // Phase 3: Very close approach - ultimate precision
            if (Math.abs(angleToTarget) < segmentAngle * 0.1) {
              // Extremely gentle final approach with exponential smoothing
              const finalApproachFactor = Math.pow(Math.abs(angleToTarget) / (segmentAngle * 0.1), 0.6);

              // Ensure we maintain enough speed to reach exactly 0 degrees
              // Higher minimum speed to ensure we reach the target
              targetSpeed = Math.max(0.002, targetSpeed * finalApproachFactor);

              // Final precision approach - ensure we reach exactly 0 degrees
              if (Math.abs(angleToTarget) < 0.01) {
                // When extremely close to target, maintain minimum speed to reach it
                targetSpeed = Math.max(0.001, targetSpeed);

                // If we're very close to 0 degrees but moving too slowly, boost speed slightly
                if (Math.abs(angleDelta) < 0.0005 && Math.abs(angleToTarget) > 0.0005) {
                  targetSpeed = 0.001; // Minimum speed to ensure we reach exactly 0
                }
              }
            }
          }
        }

        // Advanced speed blending with momentum simulation
        // Higher weight to current speed (90%) for more gradual changes
        // This creates a more natural inertia effect
        angleDelta = angleDelta * 0.9 + targetSpeed * 0.1;

        // Ensure we're moving in the correct direction
        if (angleToTarget < 0) {
          angleDelta = -Math.abs(angleDelta);
        } else {
          angleDelta = Math.abs(angleDelta);
        }

        // Ultra-precise stopping condition with zero-velocity approach
        // Use an extremely small threshold for perfect positioning
        if (Math.abs(angleToTarget) < 0.0005) { // Reduced threshold for more precision
          // Ensure we stop exactly at the target position (starting position - 0 degrees)
          angleCurrent = targetAngle; // Force exact position at starting position (0 degrees)
          finished = true;
          angleDelta = 0;

          // Log that we've forced the exact position
        }
      }

      // Ultra-smooth transition to stopped state
      if (progress >= 0.95) {
        // More gradual reduction of speed at the very end
        // Use a cubic function for smoother deceleration
        const endFactor = Math.pow(1 - Math.min(1, (progress - 0.95) / 0.05), 3);
        angleDelta *= endFactor;

        // Ensure we don't have any sudden stops
        if (angleDelta < 0.001 && angleDelta > 0) {
          // Check if we've completed rotations and are close to starting position
          if (numberOfSpins >= 10 && Math.abs(angleCurrent - targetAngle) < 0.01) {
            // Force exact position at starting position (0 degrees)
            angleCurrent = targetAngle; // 0 degrees (starting position)

          }

          finished = true;
          angleDelta = 0;
        }
      }

      if (progress >= 1) {
        // Final check to ensure we've completed rotations and are at starting position
        if (numberOfSpins >= 10) {
          // Force exact position at starting position (0 degrees)
          angleCurrent = targetAngle; // 0 degrees (starting position)
        }

        finished = true;
        angleDelta = 0;
      }
    }

    // Update the current angle
    angleCurrent += angleDelta;

    // Check if we've completed a full rotation
    while (angleCurrent >= Math.PI * 2) {
      angleCurrent -= Math.PI * 2;
      numberOfSpins++;

    }

    // Play sound when entering new segment
    if (currentSegment !== previousSegmentIndex) {
      // Log the current segment we're passing through


      previousSegmentIndex = currentSegment;
      try {
        playDingAudio()
      } catch (error) {
      }
    }

    if (finished) {
      clearInterval(timerHandle)
      spinning = false

      // Final verification to ensure we're at exactly the starting position (0 degrees)
      if (numberOfSpins >= 10 && Math.abs(angleCurrent - targetAngle) < 0.01) {
        // Force exact position at starting position for final reporting
        angleCurrent = targetAngle; // 0 degrees (starting position)
      }


      // Find the original name with commas if it exists
      const namesArray = names.split('\n')
      const filteredNames = namesArray.filter(name => name !== '')

      // Get the displayed name (without commas)
      const displayedName = segments[currentSegment]

      // Try to find the original name with commas or the exact match in the original list
      let originalName = displayedName
      let originalIndex = -1

      // First, try to find an exact match (for names without commas)
      for (let i = 0; i < filteredNames.length; i++) {
        // For names without commas, we need an exact match
        if (filteredNames[i] === displayedName) {
          originalName = filteredNames[i]
          originalIndex = i
          break
        }

        // For names with commas, we need to compare after removing commas
        const nameWithoutComma = filteredNames[i].replace(/,/g, '')
        if (nameWithoutComma === displayedName) {
          originalName = filteredNames[i]
          originalIndex = i
          break
        }
      }

      // Check if the original name had a comma
      const hadComma = originalName.includes(',')

      // Pass the final angle and segment index to the callback
      // Use the original name with commas if found
      onFinished(
        originalName,
        segColors[currentSegment],
        angleCurrent,  // Final angle in radians
        currentSegment, // Final segment index in the wheel
        originalIndex,  // Original index in the textarea (for correct removal)
        hadComma       // Whether the original name had a comma
      )

      // Play sounds and show confetti
      // Update confetti key to ensure React creates a new instance
      setConfettiKey(Date.now())
      // Trigger confetti after a small delay to ensure the key update is processed
      setTimeout(() => {
        confettiButton.current?.click()
      }, 10)

      if (applauseAudioRef.current) {
        applauseAudioRef.current.currentTime = 0
        applauseAudioRef.current.play().catch(error => {
          console.log("Audio playback failed:", error)
        })
      }

      timerHandle = 0
    }
  }


  var wheelDraw = function wheelDraw() {
    clear()
    drawWheel()
    drawNeedle()
  }

  var draw = function draw() {
    clear()
    drawWheel()
    drawNeedle()
  }
  var drawSegment = function drawSegment(key, lastAngle, angle) {
    var ctx = canvasContext
    var value = segments[key]
    var textColor = determineTextColor(segColors[key])
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, size, lastAngle, angle, false)
    ctx.lineTo(centerX, centerY)
    ctx.closePath()
    ctx.fillStyle = segColors[key]
    ctx.fill()
    ctx.stroke()
    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.rotate((lastAngle + angle) / 2)
    ctx.fillStyle = textColor
  
    // Calculate available space for text based on segment count
    // This helps determine appropriate text sizing and positioning
  
    // Enhanced responsive font size calculation based on screen size, segment count, and text length
    const textLength = value.length
  
    // Base font size calculation based on screen size and segment count
    const baseFontSize = (() => {
      // Screen size multipliers - reduced for smaller text
      const screenMultiplier = isMobile ? 0.6 : isLargeScreen ? 1.1 : 0.8
  
      // Segment count-based scaling - reduced all values
      let segmentMultiplier
      if (segments.length >= 100) {
        segmentMultiplier = 0.03
      } else if (segments.length >= 80) {
        segmentMultiplier = 0.032
      } else if (segments.length >= 70) {
        segmentMultiplier = 0.04
      } else if (segments.length >= 50) {
        segmentMultiplier = 0.06
      }
      else if (segments.length >= 40) {
        segmentMultiplier = 0.07
      } else if (segments.length >= 35) {
        segmentMultiplier = 0.065
      } else if (segments.length >= 25) {
        segmentMultiplier = 0.08
      } else if (segments.length >= 20) {
        segmentMultiplier = 0.095
      } else if (segments.length >= 10) {
        segmentMultiplier = 0.14
      } else if (segments.length >= 5) {
        segmentMultiplier = 0.41
      } else {
        segmentMultiplier = 0.55
      }
  
      return size * segmentMultiplier * screenMultiplier
    })()
  
    // Text length-based adjustment for individual segments - more aggressive reduction
    // const lengthAdjustment = (() => {
    //   if (textLength > 25) return 0.65;
    //   if (textLength > 20) return 0.7;
    //   if (textLength > 15) return 0.75;
    //   if (textLength > 12) return 0.8;
    
    //   if (textLength > 8) {
    //     return segments.length > 30 ? 1.3 : 1;
    //   }
    
    //   if (textLength >= 3) {
    //     // if (segments.length > 30) return 1.2;
    //     if (segments.length > 20) return 1.5;
    //     return 1.7;
    //   }
    
    //   // textLength < 3
    //   if (segments.length > 30) return 1.3;
    //   if (segments.length > 20) return 1.5;
    //   if (segments.length > 10) return 3;
    //   return 5;
    // })();
    const lengthAdjustment = (() => {
    
        if (segments.length < 5) {
          // For wheels with fewer than 5 segments, keep the good working logic
          if (textLength >= 20) return 0.13
          if (textLength >= 18) return 0.157
          if (textLength >= 17) return 0.167
  
          if (textLength >= 16) return 0.175
  
          if (textLength >= 15) return 0.18
  
          if (textLength >= 14) return 0.19
  
          if (textLength >= 13) return 0.20
          if (textLength >= 12) return 0.20
          if (textLength >= 11) return 0.21
          if (textLength >= 10) return 0.25
          if (textLength >= 9) return 0.29
          if (textLength >= 8) return 0.33
          if (textLength >= 7) return 0.4
  
          if (textLength >= 6) return 0.45
          if (textLength >= 5) return 0.5
          if (textLength >= 4) return 0.6
          if (textLength >= 3) return 0.9
          if (textLength >= 2) return 1.1
  
          // For very short text (1-2 chars), use smaller multiplier to prevent overflow
          return 1.4
        }
        if (segments.length < 8) {
          // For wheels with fewer than 10 segments, prevent text overflow
          if (textLength >= 24) return 0.14
          if (textLength >= 20) return 0.18
          if (textLength >= 18) return 0.22
          if (textLength >= 17) return 0.22
  
          if (textLength >= 16) return 0.21
  
          if (textLength >= 15) return 0.22
  
          if (textLength >= 14) return 0.25
  
          if (textLength >= 13) return 0.26
          if (textLength >= 12) return 0.3
          if (textLength >= 11) return 0.32
          if (textLength >= 10) return 0.35
          if (textLength >= 9) return 0.38
          if (textLength >= 8) return 0.4
          if (textLength >= 7) return 0.55
          if (textLength >= 5) return 0.6
          if (textLength >= 5) return 0.7
          if (textLength >= 4) return 0.8
          if (textLength >= 2) return 1.1
  
          // For very short text (1-2 chars), use smaller multiplier to prevent overflow
          return 1.5
        }
      
      if (segments.length < 10) {
        // For wheels with fewer than 10 segments, prevent text overflow
        if (textLength >= 30) return 0.15
        if (textLength >= 24) return 0.19
        if (textLength >= 20) return 0.2
        if (textLength >= 18) return 0.22
     

        if (textLength >= 16) return 0.25


        if (textLength >= 14) return 0.22

        if (textLength >= 13) return 0.27
        if (textLength >= 12) return 0.29
        if (textLength >= 11) return 0.32
        if (textLength >= 10) return 0.3
        if (textLength >= 9) return 0.35
        if (textLength >= 8) return 0.4
        if (textLength >= 7) return 0.4
        
        if (textLength >= 6) return 0.4
        if (textLength >= 5) return 0.6
        if (textLength >= 4) return 0.7

        if (textLength >= 2) return 1

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.1
      }
      if (segments.length ==10 && textLength < 5) {

        if (textLength >= 6) return 0.6
        if (textLength >= 5) return 0.6
        if (textLength >= 4) return 0.8

        if (textLength >= 2) return 1.8

        return 3.3

      }
      if (segments.length < 13) {
        if (textLength >= 30) return 0.5
        if (textLength >= 24) return 0.58
        if (textLength >= 20) return 0.6
        if (textLength >= 18) return 0.65
     
        if (textLength >= 17) return 0.7
        if (textLength >= 16) return 0.7

        if (textLength >= 15) return 0.75
        if (textLength >= 14) return 0.7

        if (textLength >= 13) return 0.8
        if (textLength >= 12) return 0.9
        if (textLength >= 11) return 1
        if (textLength >= 10) return 1
        if (textLength >= 9) return 1
        if (textLength >= 8) return 1.1
        if (textLength >= 7) return 1.2

        if (textLength >= 6) return 1.3
        if (textLength >= 5) return 1.5
        if (textLength >= 4) return 2.1

        if (textLength >= 2) return 1.5

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.8
      }
      if (segments.length < 16) {
        if (textLength >= 25) return 0.4
        if (textLength >= 20) return 0.5
        if (textLength >= 15) return 0.6
        if (textLength >= 13) return 0.7
        if (textLength >= 12) return 0.9
        if (textLength >= 11) return 0.8
        if (textLength >= 10) return 0.8
        if (textLength >= 9) return 0.9
        if (textLength >= 8) return 1
        if (textLength >= 7) return 1.1
        if (textLength >= 6) return 1.2
  
        if (textLength >= 4) return 1.2
        if (textLength >= 3) return 1.4
        if (textLength >= 2) return 1.5
       
        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.7
      }
      if (segments.length < 20) {
        if (textLength >= 30) return 0.5
        if (textLength >= 24) return 0.58
        if (textLength >= 20) return 0.6
        if (textLength >= 18) return 0.65
     
        if (textLength >= 17) return 0.65
       
        if (textLength >= 14) return 0.7

    
        if (textLength >= 12) return 0.8
       
        if (textLength >= 9) return 0.9
        if (textLength >= 8) return 1
        if (textLength >= 7) return 1.1

        if (textLength >= 6) return 1.3
        if (textLength >= 5) return 1.3
        if (textLength >= 4) return 1.4

        if (textLength >= 2) return 1.3

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.4
      }
      if (segments.length <= 25) {
       
     
        if (textLength >= 17) return 0.85
       
        if (textLength >= 14) return 1

    
        if (textLength >= 8) return 1.1
        if (textLength >= 7) return 1.3

        if (textLength >= 6) return 1.4
        if (textLength >= 5) return 1.5
        if (textLength >= 4) return 1.7

        if (textLength >= 2) return 1.95

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 2.2
      }
      if (segments.length <= 35) {
      
        if (textLength >= 17) return 0.85
       
        if (textLength >= 14) return 1

    
        if (textLength >= 8) return 1.1
        if (textLength >= 7) return 1.2

        if (textLength >= 6) return 1.4
        if (textLength >= 5) return 1.5
        if (textLength >= 4) return 1.7

        if (textLength >= 2) return 1.9

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.9
      }
      if (segments.length <= 45) {
     
        if (textLength >= 8) return 1.1
        if (textLength >= 7) return 1.2

        if (textLength >= 6) return 1
        if (textLength >= 5) return 1.1
        if (textLength >= 4) return 1.1

        if (textLength >= 2) return 1.2

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.8
      }
      if (segments.length <= 65) {
     
        if (textLength >= 8) return 1.1
        if (textLength >= 7) return 1.2

        if (textLength >= 6) return 1
        if (textLength >= 5) return 1.1
        if (textLength >= 4) return 1.1

        if (textLength >= 2) return 1.2

        // For very short text (1-2 chars), use smaller multiplier to prevent overflow
        return 1.8
      }
      if (textLength > 30) return 0.7
      if (textLength > 25) return 0.75
      if (textLength > 20) return 0.8
      if (textLength > 15) return 0.85
      if (textLength > 12) return 0.9
      if (textLength > 8) return 0.95

      return 1.3
    })()
  
    // Final font size combining all factors
    const fontSize = baseFontSize * lengthAdjustment
  
    // Make text slimmer for better readability with optimized weight
    // Use lighter weight (300) for better readability in small segments
    ctx.font = `400 ${fontSize}px "Quicksand"`
    ctx.textAlign = 'right'
    ctx.textBaseline = 'middle' // This centers the text vertically
  
    // Improved text positioning to better fill segments
    // Position text closer to outer edge for better visibility
    // The larger the segment count, the closer to the edge
    const edgeProximityFactor = Math.min(0.97, 0.92 + (segments.length / 500))
    const textRadius = size * edgeProximityFactor
  
    // Vertical offset for perfect centering - set to 0 since we're using textBaseline = 'middle'
    const verticalOffset = 0
  
    // Dynamic padding based on font size and segment count
    // Smaller segments need less padding
    const paddingFactor = segments.length >= 70 ? 0.08 :
                         segments.length >= 40 ? 0.1 :
                         segments.length >= 20 ? 0.12 : 0
    const paddingFromEdge = fontSize * paddingFactor
  
 
    // Calculate maximum text length based on available space and responsive font sizing
    const maxTextLength = (() => {
      // Base character limits by screen size
      const baseLimit = isMobile ? 8 : isLargeScreen ? 35 : 20

      // Adjust based on segment count - more segments = less space per segment
      let segmentAdjustment = 1.0
      if (segments.length >= 100) {
        segmentAdjustment = 0.4
      } else if (segments.length >= 70) {
        segmentAdjustment = 0.5
      } else if (segments.length >= 50) {
        segmentAdjustment = 0.6
      } else if (segments.length >= 35) {
        segmentAdjustment = 0.7
      } else if (segments.length >= 20) {
        segmentAdjustment = 0.8
      } else if (segments.length >= 10) {
        segmentAdjustment = 0.9
      }

      return Math.max(5, Math.floor(baseLimit * segmentAdjustment))
    })()

    // Apply text truncation: if text length > 18, show 15 chars + "..."
    let displayText = value
    if (value.length > 18) {
      displayText = value.substring(0, 15) + '...'
    }

    ctx.fillText(
      displayText,
      textRadius - paddingFromEdge,
      verticalOffset
    )
    ctx.restore()
  }
  var drawWheel = function drawWheel() {
    var ctx = canvasContext
    var lastAngle = angleCurrent
    var len = segments.length
    var PI2 = Math.PI * 2

    // Add shadow effect to the entire wheel
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 15
    ctx.shadowOffsetX = 5
    ctx.shadowOffsetY = 5

    // Draw outer wheel circle with shadow
    ctx.beginPath()
    ctx.arc(centerX, centerY, size, 0, PI2, false)
    ctx.closePath()
    ctx.lineWidth = 10
    ctx.strokeStyle = primaryColor
    ctx.stroke()

    // Reset shadow for segments
    ctx.restore()

    ctx.lineWidth = 1
    ctx.strokeStyle = primaryColor
    ctx.textAlign = 'center'
    ctx.font = '1em ' + fontFamily

    // Draw segments - no offset needed as 0° is already at the right side
    // in the standard canvas coordinate system
    for (var i = 1; i <= len; i++) {
      var angle = PI2 * (i / len) + angleCurrent
      drawSegment(i - 1, lastAngle, angle)
      lastAngle = angle
    }

    // Draw angle markers (90°, 180°, 270°, 360°/0°)
    // Function to draw angle markers
    const drawAngleMarkers = (ctx: CanvasRenderingContext2D) => {
      const markerLength = size * 0.15; // Length of the marker line
      const textDistance = size * 1.15; // Distance of text from center

      // Save current context state
      ctx.save();

      // Set styles for markers
      ctx.lineWidth = 3;
      ctx.strokeStyle = 'red';
      ctx.fillStyle = 'red';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Draw 0°/360° marker (right)
      ctx.beginPath();
      ctx.moveTo(centerX + size, centerY);
      ctx.lineTo(centerX + size + markerLength, centerY);
      ctx.stroke();
      ctx.fillText('0°/360°', centerX + textDistance, centerY);

      // Draw 90° marker (bottom)
      ctx.beginPath();
      ctx.moveTo(centerX, centerY + size);
      ctx.lineTo(centerX, centerY + size + markerLength);
      ctx.stroke();
      ctx.fillText('90°', centerX, centerY + textDistance);

      // Draw 180° marker (left)
      ctx.beginPath();
      ctx.moveTo(centerX - size, centerY);
      ctx.lineTo(centerX - size - markerLength, centerY);
      ctx.stroke();
      ctx.fillText('180°', centerX - textDistance, centerY);

      // Draw 270° marker (top)
      ctx.beginPath();
      ctx.moveTo(centerX, centerY - size);
      ctx.lineTo(centerX, centerY - size - markerLength);
      ctx.stroke();
      ctx.fillText('270°', centerX, centerY - textDistance);

      // Draw 45° marker (bottom-right)
      ctx.beginPath();
      ctx.moveTo(centerX + size * Math.cos(Math.PI/4), centerY + size * Math.sin(Math.PI/4));
      ctx.lineTo(centerX + (size + markerLength) * Math.cos(Math.PI/4), centerY + (size + markerLength) * Math.sin(Math.PI/4));
      ctx.stroke();
      ctx.fillText('45°', centerX + textDistance * Math.cos(Math.PI/4), centerY + textDistance * Math.sin(Math.PI/4));

      // Draw 135° marker (bottom-left)
      ctx.beginPath();
      ctx.moveTo(centerX - size * Math.cos(Math.PI/4), centerY + size * Math.sin(Math.PI/4));
      ctx.lineTo(centerX - (size + markerLength) * Math.cos(Math.PI/4), centerY + (size + markerLength) * Math.sin(Math.PI/4));
      ctx.stroke();
      ctx.fillText('135°', centerX - textDistance * Math.cos(Math.PI/4), centerY + textDistance * Math.sin(Math.PI/4));

      // Draw 225° marker (top-left)
      ctx.beginPath();
      ctx.moveTo(centerX - size * Math.cos(Math.PI/4), centerY - size * Math.sin(Math.PI/4));
      ctx.lineTo(centerX - (size + markerLength) * Math.cos(Math.PI/4), centerY - (size + markerLength) * Math.sin(Math.PI/4));
      ctx.stroke();
      ctx.fillText('225°', centerX - textDistance * Math.cos(Math.PI/4), centerY - textDistance * Math.sin(Math.PI/4));

      // Draw 315° marker (top-right)
      ctx.beginPath();
      ctx.moveTo(centerX + size * Math.cos(Math.PI/4), centerY - size * Math.sin(Math.PI/4));
      ctx.lineTo(centerX + (size + markerLength) * Math.cos(Math.PI/4), centerY - (size + markerLength) * Math.sin(Math.PI/4));
      ctx.stroke();
      ctx.fillText('315°', centerX + textDistance * Math.cos(Math.PI/4), centerY - textDistance * Math.sin(Math.PI/4));

      // Restore context state
      ctx.restore();
    };

    // Call the function to draw the markers
    // drawAngleMarkers(ctx);

    // Draw center button
    ctx.save()
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)'
    ctx.shadowBlur = 10
    ctx.shadowOffsetX = 2
    ctx.shadowOffsetY = 2

    ctx.beginPath()
    ctx.arc(centerX, centerY, isMobile ? 20 : 60, 0, PI2, false)
    ctx.fillStyle = '#fff'
    ctx.fill()
    ctx.closePath()

    ctx.restore()

    ctx.fillStyle = primaryColor
    ctx.lineWidth = 10
    ctx.strokeStyle = contrastColor
    ctx.fill()
    ctx.fillStyle = contrastColor
    ctx.textAlign = 'center'

    // Optimize button text size based on screen size
    const buttonFontSize = isMobile ? '0.9em' : isLargeScreen ? '1.2em' : '1em'
    ctx.font = `bold ${buttonFontSize} ${fontFamily}`

    // Improved vertical positioning
    const buttonTextOffset = isMobile ? 2 : 3

    // Change button text based on whether wheel has been spun
    const displayText = hasBeenSpun ? '✓' : buttonText
    ctx.fillText(displayText, centerX, centerY + buttonTextOffset)
  }
  var drawNeedle = function drawNeedle() {
    var ctx = canvasContext
    ctx.lineWidth = 1
    ctx.strokeStyle = contrastColor
    ctx.fillStyle = contrastColor
    ctx.beginPath()
    ctx.closePath()
    ctx.fill()
    ctx.restore()
    // No offset needed as 0° is already at the right side
    var change = angleCurrent;
    var i = segments.length - Math.floor((change / (Math.PI * 2)) * segments.length) - 1
    if (i < 0) i = i + segments.length
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillStyle = primaryColor

    // Update arrow color state for real-time color changes during rotation
    if (i >= 0 && i < segColors.length && i !== currentArrowSegment) {
      setCurrentArrowSegment(i)
      setCurrentArrowColor(segColors[i])
    }

    // Draw current angle indicator
    const currentAngleDegrees = (angleCurrent * 180 / Math.PI) % 360;
    ctx.save();
    ctx.font = 'bold 20px Arial';
    ctx.fillStyle = '#FF5722'; // Orange color for visibility
    ctx.textAlign = 'center';
    ctx.fillText(`Current: ${currentAngleDegrees.toFixed(2)}°`, centerX, centerY + size + 50);
    ctx.restore();

    // Add subtle text shadow for better readability of result text
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
    ctx.shadowBlur = 3
    ctx.shadowOffsetX = 1
    ctx.shadowOffsetY = 1

    // Optimize displayed segment text size based on screen size and text length
    const selectedSegment = segments[i]
    const selectedSegmentLength = selectedSegment ? selectedSegment.length : 0

    // Adjust font size based on text length and screen size
    const resultFontSize = (() => {
      if (isMobile) {
        return selectedSegmentLength > 15 ? '1.1em' : selectedSegmentLength > 10 ? '1.3em' : '1.5em'
      } else if (isLargeScreen) {
        return selectedSegmentLength > 20 ? '1.6em' : selectedSegmentLength > 15 ? '1.8em' : '2em'
      } else {
        return selectedSegmentLength > 15 ? '1.3em' : selectedSegmentLength > 10 ? '1.5em' : '1.7em'
      }
    })()

    ctx.font = `bold ${resultFontSize} ${fontFamily}`

    // Improved positioning for the result text
    const resultXOffset = isMobile ? 5 : 10
    const resultYOffset = isMobile ? 40 : isLargeScreen ? 60 : 50

    currentSegment = i

    // Apply same text truncation for result display
    let resultDisplayText = segments[currentSegment]
    if (segments[currentSegment] && segments[currentSegment].length > 18) {
      resultDisplayText = segments[currentSegment].substring(0, 15) + '...'
    }

    isStarted && ctx.fillText(
      resultDisplayText,
      centerX + resultXOffset,
      centerY + size + resultYOffset
    )
  }

  var clear = function clear() {
    var canvas = document.getElementById('canvas')
    var ctx = canvasContext
    ctx.clearRect(0, 0, (canvas as any).width, (canvas as any).height)
  }

  // Initialize audio in useEffect
  React.useEffect(() => {
    // Create new audio instance
    applauseAudioRef.current = new Audio(applauseSound)

    // Clean up on unmount
    return () => {
      if (applauseAudioRef.current) {
        applauseAudioRef.current.pause()
        applauseAudioRef.current = null
      }
    }
  }, [])

  // Add cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (timerHandle) {
        clearInterval(timerHandle)
      }
      resetWheel()
    }
  }, [])

  if (names.trim() === '') return <Header>Enter some names to get started</Header>
  else
    return (
      <>
     {/* Confetti - with unique key to prevent React warnings */}
     <ConfettiExplosion key={`confetti-${confettiKey}`} ref={confettiButton} />
        <div
          id='wheel'
          style={{
           
      
            transform: 'rotate(0deg)',
            position: 'relative',
            // marginTop: '60px',
            marginBottom: '20px',
            padding: '10px'
          }}
        >
          <canvas
            ref={canvasRef}
            id='canvas'
            key={`wheel-${memoizedSegments.length}-${names.length}-${colors.join('-')}`} // Force re-render when segments or colors change
            width={centerX * 2}
            height={centerY * 2}
            style={{
              pointerEvents: isFinished && isOnlyOnce ? 'none' : 'auto',
              textAlign: 'center',
              animation: wheelClicked ? '' : 'rotateAnimation 15s linear infinite'
            }}
          />

          <div
            style={{
              position: 'absolute',
              top: '50%',
              right: '-10px',
              transform: 'translateY(-50%)',
              zIndex: 10
            }}
          >
            <svg
              width={`${size * 0.18}px`}
              height={`${size * 0.22}px`}
              viewBox="0 0 512.013 512.013"
              style={{
                filter: 'drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3))',
                transition: 'fill 0.1s ease'
              }}
            >
              <path
                d="M366.64,256.013L508.677,32.802c5.141-8.107,4.267-18.624-2.176-25.749c-6.443-7.168-16.832-9.067-25.365-4.8
                L11.802,236.92c-7.232,3.627-11.797,11.008-11.797,19.093c0,8.085,4.565,15.467,11.797,19.093l469.333,234.667
                c3.029,1.515,6.293,2.24,9.536,2.24c5.888,0,11.691-2.432,15.829-7.04c6.443-7.125,7.317-17.643,2.176-25.749L366.64,256.013z"
                fill={currentArrowColor}
              />
            </svg>
          </div>

          {/* SVG Text */}
          {!wheelClicked && (
            <>
              <svg
                viewBox='0 0 380 380'
                style={{
                  position: 'absolute',
               
                  left: 5,
                  transform: 'rotate(32deg)'
                }}
                onClick={() => {
                  spin()
                }}
              >
                <path
                  id='curve'
                  d='M73.2,148.6c4-6.1,65.5-96.8,178.6-95.6c111.3,1.2,170.8,90.3,175.1,97'
                  fill='transparent'
                />
                <text width='500'>
                  <textPath
                    xlinkHref='#curve'
                    style={{
                      fontSize: '28px',
                      fontWeight: 500,
                      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.7)',
                      letterSpacing: '2px'
                    }}
                    fill='#fff'
                  >
                    Click to spin
                  </textPath>
                </text>
              </svg>
              {/* Bottom "or press ctrl+enter" text */}
              <svg
                viewBox='0 0 220 220'
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: '0',
                  transform: isMobile?`translate(-1%, -45px) rotate(91deg)`:`translate(-1%, -80px) rotate(91deg)`,
                }}
                onClick={() => {
                  spin()
                }}
              >
                <path
                  fill='transparent'
                  d="M0,110a110,110 0 1,0 220,0a110,110 0 1,0 -220,0"
                />
                <path
                  fill="none"
                  id="innerCircle"
                  d="M10,110a100,100 0 1,0 200,0a100,100 0 1,0 -200,0"
                />

              </svg>
            </>
          )}
        </div>
      </>
    )
}

export default WheelComponent